<template>
  <div class="wallet-connect">
    <!-- Stage 3: Fully Authenticated State -->
    <div v-if="isConnected && address && isAuthenticated" class="relative">
      <!-- Profile Section (Clickable) -->
      <div
        class="flex items-center gap-brutal-sm cursor-pointer border-brutal bg-brutal-white px-3 py-2 shadow-brutal hover-brutal-electric mobile-tap"
        @click="toggleProfileDropdown"
        v-motion
        :initial="{ opacity: 0, scale: 0.95 }"
        :enter="{ opacity: 1, scale: 1, transition: { duration: 200 } }"
      >
        <!-- Address Display -->
        <div class="font-mono-brutal text-xs text-brutal-black">
          {{ formatAddress(address) }}
        </div>

        <!-- ETH Balance -->
        <div v-if="balance" class="border-brutal bg-neon-lime px-2 py-1 font-mono-brutal text-xs text-brutal-black ml-2">
          {{ formatBalance(balance.value, balance.decimals) }} ETH
        </div>

        <!-- Verified Indicator -->
        <!-- <div class="border-brutal bg-acid-green px-2 py-1 font-mono-brutal text-xs text-brutal-black">
          ✓
        </div> -->

        <!-- Dropdown Arrow -->
        <Icon name="lucide:chevron-down" class="h-4 w-4 text-brutal-black transition-transform duration-200" :class="{ 'rotate-180': showProfileDropdown }" />
      </div>

      <!-- Profile Dropdown Menu -->
      <div
        v-if="showProfileDropdown"
        class="absolute right-0 top-full mt-2 w-48 border-brutal bg-brutal-black shadow-brutal-lg z-50"
        v-motion
        :initial="{ opacity: 0, y: -10 }"
        :enter="{ opacity: 1, y: 0, transition: { duration: 200 } }"
        :leave="{ opacity: 0, y: -10, transition: { duration: 150 } }"
      >
        <div class="p-2 space-y-2">
          <!-- Profile Button -->
          <Button
            class="w-full border-brutal bg-electric-blue px-4 py-3 font-brutal text-sm uppercase text-brutal-white shadow-brutal hover-brutal-neon mobile-tap text-left"
            @click="handleProfileClick"
          >
            <Icon name="lucide:user" class="h-4 w-4 mr-2" />
            PROFILE
          </Button>

          <!-- Logout Button -->
          <Button
            class="w-full border-brutal bg-neon-pink px-4 py-3 font-brutal text-sm uppercase text-brutal-white shadow-brutal hover-brutal-electric mobile-tap text-left"
            @click="handleLogout"
          >
            <Icon name="lucide:log-out" class="h-4 w-4 mr-2" />
            LOGOUT
          </Button>
        </div>
      </div>
    </div>

    <!-- Stage 2: Connected but Not Authenticated -->
    <div v-else-if="isConnected && address" class="flex items-center gap-brutal-sm">
      <!-- Address and Balance Display
      <div class="hidden sm:flex items-center gap-brutal-sm">
        <div class="border-brutal bg-brutal-white px-3 py-2 font-mono-brutal text-xs text-brutal-black">
          {{ formatAddress(address) }}
        </div>
        <div v-if="balance" class="border-brutal bg-neon-lime px-3 py-2 font-mono-brutal text-xs text-brutal-black">
          {{ formatBalance(balance.value, balance.decimals) }} ETH
        </div>
      </div> -->

      <!-- Signature Required Button -->
      <Button
        class="border-brutal bg-neon-orange px-4 py-2 sm:px-6 sm:py-3 font-brutal text-xs sm:text-sm uppercase text-brutal-black shadow-brutal hover-brutal-electric mobile-tap"
        @click="handleSignIn"
        :disabled="isSigningIn"
        v-motion
        :initial="{ opacity: 0, x: 20 }"
        :enter="{ opacity: 1, x: 0, transition: { duration: 300 } }"
      >
        <Icon v-if="isSigningIn" name="lucide:loader-2" class="h-4 w-4 mr-2 animate-spin" />
        <Icon v-else name="lucide:pen-tool" class="h-4 w-4 mr-2" />
        {{ isSigningIn ? 'SIGNING...' : 'REQUIRE SIGNATURE' }}
      </Button>

      <!-- Disconnect Button -->
      <Button
        class="border-brutal bg-brutal-charcoal px-4 py-2 font-brutal text-xs uppercase text-brutal-white shadow-brutal hover-brutal mobile-tap"
        @click="disconnect"
      >
        <Icon name="lucide:x" class="h-4 w-4 mr-1" />
        DISCONNECT
      </Button>
    </div>

    <!-- Stage 1: Disconnected State -->
    <div v-else>
      <Button
        class="border-brutal-thick bg-hot-magenta px-4 py-2 sm:px-6 sm:py-3 font-brutal text-xs sm:text-sm uppercase text-brutal-white shadow-brutal hover-brutal-electric mobile-tap"
        @click="openConnectModal"
        v-motion
        :initial="{ opacity: 0, scale: 0.95 }"
        :enter="{ opacity: 1, scale: 1, transition: { duration: 200 } }"
        :whileHover="{ scale: 1.05, transition: { duration: 150 } }"
      >
        <Icon name="lucide:wallet" class="h-4 w-4 mr-2" />
        <span class="hidden sm:inline">CONNECT </span>WALLET
      </Button>
    </div>

    <!-- Connect Modal -->
    <Sheet v-model:open="showConnectModal">
      <SheetContent side="right" class="w-80 bg-brutal-black border-brutal-heavy">
        <SheetHeader>
          <SheetTitle class="font-brutal text-2xl text-neon-lime">CONNECT WALLET</SheetTitle>
        </SheetHeader>

        <div class="mt-8 space-y-4">
          <div v-if="connectError" class="border-brutal bg-neon-pink px-4 py-3 font-mono-brutal text-sm text-brutal-white">
            {{ connectError.message }}
          </div>

          <div v-for="connector in connectors" :key="connector.id" class="space-y-2">
            <Button
              class="w-full border-brutal bg-neon-lime px-6 py-4 font-brutal text-sm uppercase text-brutal-black shadow-brutal hover-brutal-electric mobile-tap"
              @click="connectWithConnector(connector)"
              :disabled="isConnecting"
            >
              {{ isConnecting ? 'CONNECTING...' : connector.name }}
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  </div>
</template>

<script setup lang="ts">
import { useAccount, useBalance, useConnect, useDisconnect } from '@wagmi/vue';
import { formatUnits } from 'viem';
import { onMounted, onUnmounted, ref } from 'vue';
import { useAuth } from '@/composables/useAuth';
import { useWalletConnect } from '@/composables/useWalletConnect';

// Wagmi composables
const { address, isConnected } = useAccount();
const {
  connectors,
  connect,
  error: connectError,
  isPending: isConnecting,
} = useConnect();
const { disconnect } = useDisconnect();
const { data: balance } = useBalance({ address });

// Authentication composable
const { isAuthenticated, signIn, logout, isSigningIn } = useAuth();

// Wallet connect composable
const { showConnectModal, openConnectModal } = useWalletConnect();

// Local dropdown state
const showProfileDropdown = ref(false);

// Methods
const toggleProfileDropdown = () => {
  showProfileDropdown.value = !showProfileDropdown.value;
};

const handleProfileClick = () => {
  showProfileDropdown.value = false;
  // TODO: Navigate to profile page
  console.log('Navigate to profile page');
};

// biome-ignore lint/suspicious/noExplicitAny: Wagmi connector type is complex
const connectWithConnector = async (connector: any) => {
  try {
    connect({ connector });
    showConnectModal.value = false;
  } catch (error) {
    console.error('Connection failed:', error);
  }
};

const handleSignIn = async () => {
  if (!address.value) return;

  try {
    await signIn(address.value);
  } catch (error) {
    console.error('Sign in failed:', error);
  }
};

const handleLogout = async () => {
  showProfileDropdown.value = false;
  await logout();
  disconnect();
};

const formatAddress = (addr: string) => {
  return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
};

const formatBalance = (value: bigint, decimals: number) => {
  return Number.parseFloat(formatUnits(value, decimals)).toFixed(4);
};

// Click outside handler to close dropdown
const handleClickOutside = (event: Event) => {
  const target = event.target as Element;
  if (!target.closest('.wallet-connect')) {
    showProfileDropdown.value = false;
  }
};

// Setup click outside listener
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>