<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import type { HTMLAttributes } from 'vue';
import { cn } from '@/lib/utils';

const props = defineProps<{
  defaultValue?: string;
  modelValue?: string;
  class?: HTMLAttributes['class'];
  placeholder?: string;
  disabled?: boolean;
  readonly?: boolean;
  rows?: number;
}>();

const emits = defineEmits<{
  'update:modelValue': [payload: string];
}>();

const modelValue = useVModel(props, 'modelValue', emits, {
  passive: true,
  defaultValue: props.defaultValue,
});
</script>

<template>
  <textarea
    v-model="modelValue"
    data-slot="textarea"
    :placeholder="placeholder"
    :disabled="disabled"
    :readonly="readonly"
    :rows="rows || 3"
    :class="
      cn(
        'flex min-h-[80px] w-full border-brutal bg-brutal-white px-3 py-2 text-sm font-mono-brutal text-brutal-black placeholder:text-brutal-black/50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-neon-lime focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 shadow-brutal brutal-override resize-none',
        props.class,
      )
    "
  />
</template>
