<script setup lang="ts">
import { reactiveOmit } from '@vueuse/core';
import { Switch, type SwitchProps } from 'reka-ui';
import type { HTMLAttributes } from 'vue';
import { cn } from '@/lib/utils';

const props = defineProps<
  SwitchProps & {
    class?: HTMLAttributes['class'];
  }
>();

const delegatedProps = reactiveOmit(props, 'class');
</script>

<template>
  <Switch
    data-slot="switch"
    v-bind="delegatedProps"
    :class="
      cn(
        'peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center border-brutal bg-brutal-white shadow-brutal transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-neon-lime data-[state=unchecked]:bg-brutal-charcoal brutal-override',
        props.class,
      )
    "
  >
    <div
      class="pointer-events-none block h-4 w-4 border-brutal bg-brutal-white shadow-brutal transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-1 brutal-override"
    />
  </Switch>
</template>
