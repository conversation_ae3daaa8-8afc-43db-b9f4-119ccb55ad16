<script setup lang="ts">
import type { HTMLAttributes } from 'vue';
import { cn } from '@/lib/utils';

const props = defineProps<{
  class?: HTMLAttributes['class'];
}>();
</script>

<template>
  <div
    data-slot="avatar"
    :class="
      cn(
        'relative flex h-10 w-10 shrink-0 overflow-hidden border-brutal bg-brutal-white shadow-brutal brutal-override',
        props.class,
      )
    "
  >
    <slot />
  </div>
</template>
