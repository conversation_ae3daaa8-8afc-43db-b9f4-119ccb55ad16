<script setup lang="ts">
import type { HTMLAttributes } from 'vue';
import { cn } from '@/lib/utils';

const props = defineProps<{
  class?: HTMLAttributes['class'];
}>();
</script>

<template>
  <div
    data-slot="avatar-fallback"
    :class="
      cn(
        'flex h-full w-full items-center justify-center bg-brutal-charcoal text-brutal-white font-brutal text-sm',
        props.class,
      )
    "
  >
    <slot />
  </div>
</template>
