<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { HTMLAttributes } from 'vue';
import { cn } from '@/lib/utils';

const props = defineProps<{
  src?: string;
  alt?: string;
  class?: HTMLAttributes['class'];
}>();

const emit = defineEmits<{
  loadingStatusChange: [status: 'idle' | 'loading' | 'loaded' | 'error'];
}>();

const imageRef = ref<HTMLImageElement>();
const imageLoadingStatus = ref<'idle' | 'loading' | 'loaded' | 'error'>('idle');

const handleLoad = () => {
  imageLoadingStatus.value = 'loaded';
  emit('loadingStatusChange', 'loaded');
};

const handleError = () => {
  imageLoadingStatus.value = 'error';
  emit('loadingStatusChange', 'error');
};

onMounted(() => {
  if (props.src) {
    imageLoadingStatus.value = 'loading';
    emit('loadingStatusChange', 'loading');
  }
});
</script>

<template>
  <img
    v-if="src"
    ref="imageRef"
    data-slot="avatar-image"
    :src="src"
    :alt="alt"
    :class="cn('aspect-square h-full w-full object-cover', props.class)"
    @load="handleLoad"
    @error="handleError"
  />
</template>
