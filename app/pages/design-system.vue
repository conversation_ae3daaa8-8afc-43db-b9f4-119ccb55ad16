<script setup lang="ts">
// Import new components
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';

// SEO and meta
useHead({
  title: 'Design System - DEFI.AI',
  meta: [
    {
      name: 'description',
      content:
        'Neo-Brutalism design system showcase with mobile-responsive components.',
    },
  ],
});

// Sample data for components
const chartData = [
  { label: 'AAVE', value: 1105555, color: 'bg-neon-lime', percentage: 45 },
  { label: 'UNISWAP', value: 859876, color: 'bg-neon-cyan', percentage: 35 },
  { label: 'LIDO', value: 491358, color: 'bg-plasma-orange', percentage: 20 },
];

const colorPalette = [
  { name: 'Electric Blue', class: 'bg-electric-blue', hex: '#0066ff' },
  { name: 'Hot Magenta', class: 'bg-hot-magenta', hex: '#ff006e' },
  { name: 'Acid Green', class: 'bg-acid-green', hex: '#39ff14' },
  { name: 'Laser Red', class: 'bg-laser-red', hex: '#ff073a' },
  { name: 'Cyber Purple', class: 'bg-cyber-purple', hex: '#8a2be2' },
  { name: 'Toxic Yellow', class: 'bg-toxic-yellow', hex: '#ffff00' },
  { name: 'Neon Violet', class: 'bg-neon-violet', hex: '#9d00ff' },
  { name: 'Plasma Orange', class: 'bg-plasma-orange', hex: '#ff4500' },
];

const buttonVariants = [
  {
    label: 'Primary',
    class: 'bg-electric-blue text-brutal-white hover-brutal-electric',
  },
  {
    label: 'Secondary',
    class: 'bg-acid-green text-brutal-black hover-brutal-neon',
  },
  {
    label: 'Danger',
    class: 'bg-laser-red text-brutal-white hover-brutal-magenta',
  },
  {
    label: 'Warning',
    class: 'bg-toxic-yellow text-brutal-black hover-brutal-cyan',
  },
];
